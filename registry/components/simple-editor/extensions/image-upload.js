import { Node, mergeAttributes } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import ImageNodeView from '../components/ImageNodeView.vue'


export const ImageUpload = Node.create({
  name: 'imageUpload',

  group: 'block',

  content: '',

  isolating: true,


  addAttributes() {
    return {
      src: {
        default: null,
      },
      alt: {
        default: null,
      },
      title: {
        default: null,
      },
      width: {
        default: '50%',
      },
      height: {
        default: null,
      },
      align: {
        default: 'center',
      },
      uploading: {
        default: false,
      },
      progress: {
        default: 0,
      },
      error: {
        default: null,
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'p.image-paragraph',
        getAttrs: (element) => {
          const img = element.querySelector('img')
          if (!img) return false

          return {
            src: img.getAttribute('src'),
            alt: img.getAttribute('alt'),
            title: img.getAttribute('title'),
            width: img.getAttribute('width') || img.style.width,
            height: img.getAttribute('height') || img.style.height,
            align: element.getAttribute('data-align') || 'center',
          }
        },
      },
      {
        tag: 'img[src]',
        getAttrs: (element) => ({
          src: element.getAttribute('src'),
          alt: element.getAttribute('alt'),
          title: element.getAttribute('title'),
          width: element.getAttribute('width') || element.style.width,
          height: element.getAttribute('height') || element.style.height,
          align: element.getAttribute('data-align') || 'center',
        }),
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    const { uploading, progress, error, align, width, ...attrs } = HTMLAttributes

    const paragraphClass = `image-paragraph align-${align || 'center'}`
    const imgStyle = width ? `width: ${width}; max-width: 100%; height: auto;` : 'max-width: 100%; height: auto;'

    if (uploading) {
      return [
        'p',
        {
          class: paragraphClass,
          'data-align': align || 'center'
        },
        [
          'span',
          { class: 'image-upload-container uploading' },
          [
            'img',
            mergeAttributes(attrs, {
              style: `opacity: 0.5; ${imgStyle}`,
              width: width || '50%'
            })
          ],
          [
            'span',
            { class: 'upload-progress' },
            [
              'span',
              {
                class: 'progress-bar',
                style: `width: ${progress}%`
              }
            ]
          ],
          [
            'span',
            { class: 'upload-text' },
            `Uploading... ${progress}%`
          ]
        ]
      ]
    }

    if (error) {
      return [
        'p',
        {
          class: paragraphClass,
          'data-align': align || 'center'
        },
        [
          'span',
          { class: 'image-upload-container error' },
          [
            'span',
            { class: 'error-message' },
            `Upload failed: ${error}`
          ],
          [
            'button',
            {
              class: 'retry-button',
              onclick: 'this.closest(".image-upload-container").remove()'
            },
            'Remove'
          ]
        ]
      ]
    }

    return [
      'p',
      {
        class: paragraphClass,
        'data-align': align || 'center'
      },
      [
        'img',
        mergeAttributes(attrs, {
          style: imgStyle,
          width: width || '50%',
          'data-align': align || 'center'
        })
      ]
    ]
  },

  addNodeView() {
    return VueNodeViewRenderer(ImageNodeView)
  },

  addCommands() {
    const self = this

    const validateFile = (file) => {
      const { allowedTypes, maxFileSize } = self.options
      if (!allowedTypes.includes(file.type)) {
        console.error('File type not allowed:', file.type)
        return false
      }
      if (file.size > maxFileSize) {
        console.error('File too large:', file.name)
        return false
      }
      return true
    }

    const uploadFile = (file, view, pos) => {
      const nodePos = pos
      self.options.uploadFn(file, (progress) => {
        const currentTr = view.state.tr
        console.log('ahahahahah', progress)
        const currentNode = view.state.doc.nodeAt(nodePos)
        if (currentNode && currentNode.type.name === self.name) {
          const newAttrs = { ...currentNode.attrs, progress }
          currentTr.setNodeMarkup(nodePos, null, newAttrs)
          view.dispatch(currentTr)
        }
      }).then((url) => {
        const currentTr = view.state.tr
        const currentNode = view.state.doc.nodeAt(nodePos)
        if (currentNode && currentNode.type.name === self.name) {
          const newAttrs = {
            ...currentNode.attrs,
            src: url,
            uploading: false,
            progress: 100,
            error: null,
          }
          currentTr.setNodeMarkup(nodePos, null, newAttrs)
          view.dispatch(currentTr)
        }
      }).catch((error) => {
        const currentTr = view.state.tr
        const currentNode = view.state.doc.nodeAt(nodePos)
        if (currentNode && currentNode.type.name === self.name) {
          const newAttrs = {
            ...currentNode.attrs,
            uploading: false,
            error: error?.message || String(error),
          }
          currentTr.setNodeMarkup(nodePos, null, newAttrs)
          view.dispatch(currentTr)
        }
      })
    }

    return {
      setImage: (options) => ({ commands }) => {
        return commands.insertContent({
          type: self.name,
          attrs: options,
        })
      },
      uploadImage: (file) => ({ tr, dispatch, state, view }) => {
        if (!validateFile(file)) {
          return false
        }

        const { schema } = state
        const node = schema.nodes[self.name].create({
          uploading: true,
          progress: 0,
          src: URL.createObjectURL(file),
          alt: file.name,
          width: '50%',
          align: 'center',
        })

        const insertPos = state.selection.from
        const transaction = tr.insert(insertPos, node)
        dispatch(transaction)
        debugger
        // 开始上传
        uploadFile(file, view, insertPos)

        return true
      },
      _validateImageFile: () => validateFile,
      _uploadImageFile: () => uploadFile,
    }
  },

  addOptions() {
    return {
      uploadFn: async (_file, onProgress) => {
        // 默认的上传函数，需要用户自定义
        return new Promise((resolve) => {
          // 模拟上传进度
          let progress = 0
          const interval = setInterval(() => {
            progress += Math.random() * 20
            if (progress > 100) progress = 100
            onProgress(Math.floor(progress))
            if (progress >= 100) {
              clearInterval(interval)
              // 这里应该返回真实的图片URL
              setTimeout(() => {
                resolve('https://picsum.photos/400/300?random=' + Date.now())
              }, 500)
            }
          }, 200)
        })
      },
      maxFileSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    }
  },

  // helper instance methods are unnecessary; closures are used inside commands/plugins

  // addProseMirrorPlugins() {
  //   const self = this
  //   return [
  //     new Plugin({
  //       key: new PluginKey('imageUpload'),
  //       props: {
  //         handleDOMEvents: {
  //           drop: (view, event) => {
  //             const hasFiles = event.dataTransfer?.files?.length > 0
  //             if (!hasFiles) return false

  //             const files = Array.from(event.dataTransfer.files)
  //             const imageFiles = files.filter(file =>
  //               self.options.allowedTypes.includes(file.type)
  //             )

  //             if (imageFiles.length === 0) return false

  //             event.preventDefault()

  //             const coordinates = view.posAtCoords({
  //               left: event.clientX,
  //               top: event.clientY,
  //             })

  //             const validateFile = (file) => {
  //               const { allowedTypes, maxFileSize } = self.options
  //               if (!allowedTypes.includes(file.type)) return false
  //               if (file.size > maxFileSize) return false
  //               return true
  //             }
  //             const uploadFile = (file, v, pos) => {
  //               const nodePos = pos
  //               self.options.uploadFn(file, (progress) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = { ...currentNode.attrs, progress }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               }).then((url) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = {
  //                     ...currentNode.attrs,
  //                     src: url,
  //                     uploading: false,
  //                     progress: 100,
  //                     error: null,
  //                   }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               }).catch((error) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = {
  //                     ...currentNode.attrs,
  //                     uploading: false,
  //                     error: error?.message || String(error),
  //                   }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               })
  //             }

  //             imageFiles.forEach((file) => {
  //               if (validateFile(file)) {
  //                 uploadFile(file, view, coordinates?.pos || view.state.selection.from)
  //               }
  //             })

  //             return true
  //           },
  //           paste: (view, event) => {
  //             const hasFiles = event.clipboardData?.files?.length > 0
  //             if (!hasFiles) return false

  //             const files = Array.from(event.clipboardData.files)
  //             const imageFiles = files.filter(file =>
  //               self.options.allowedTypes.includes(file.type)
  //             )

  //             if (imageFiles.length === 0) return false

  //             event.preventDefault()

  //             const validateFile = (file) => {
  //               const { allowedTypes, maxFileSize } = self.options
  //               if (!allowedTypes.includes(file.type)) return false
  //               if (file.size > maxFileSize) return false
  //               return true
  //             }
  //             const uploadFile = (file, v, pos) => {
  //               const nodePos = pos
  //               self.options.uploadFn(file, (progress) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = { ...currentNode.attrs, progress }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               }).then((url) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = {
  //                     ...currentNode.attrs,
  //                     src: url,
  //                     uploading: false,
  //                     progress: 100,
  //                     error: null,
  //                   }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               }).catch((error) => {
  //                 const currentTr = v.state.tr
  //                 const currentNode = v.state.doc.nodeAt(nodePos)
  //                 if (currentNode && currentNode.type.name === self.name) {
  //                   const newAttrs = {
  //                     ...currentNode.attrs,
  //                     uploading: false,
  //                     error: error?.message || String(error),
  //                   }
  //                   currentTr.setNodeMarkup(nodePos, null, newAttrs)
  //                   v.dispatch(currentTr)
  //                 }
  //               })
  //             }

  //             imageFiles.forEach((file) => {
  //               if (validateFile(file)) {
  //                 uploadFile(file, view, view.state.selection.from)
  //               }
  //             })

  //             return true
  //           },
  //         },
  //       },
  //     }),
  //   ]
  // },
})
