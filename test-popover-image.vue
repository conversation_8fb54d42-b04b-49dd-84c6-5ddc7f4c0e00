<template>
  <div class="test-container">
    <h2>图片设置 Popover 测试</h2>
    <p>点击图片可以看到设置面板悬浮显示</p>
    
    <div class="editor-wrapper">
      <SimpleEditor />
    </div>
    
    <div class="instructions">
      <h3>测试步骤：</h3>
      <ol>
        <li>点击工具栏中的图片按钮上传或插入图片</li>
        <li>点击图片进行选中</li>
        <li>观察图片附近是否出现设置面板（Popover）</li>
        <li>在设置面板中调整对齐方式和大小</li>
        <li>点击其他地方关闭设置面板</li>
      </ol>
      
      <h3>预期效果：</h3>
      <ul>
        <li>✅ 选中图片时显示蓝色边框</li>
        <li>✅ 自动在图片上方显示设置 Popover</li>
        <li>✅ 对齐按钮可以切换图片对齐方式</li>
        <li>✅ 滑块可以调整图片大小</li>
        <li>✅ 快速大小按钮可以快速设置尺寸</li>
        <li>✅ 点击外部区域关闭 Popover</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import SimpleEditor from './registry/components/simple-editor/index.vue'
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.editor-wrapper {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background: white;
  min-height: 300px;
}

.instructions {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.instructions h3 {
  color: #374151;
  margin-top: 0;
  margin-bottom: 12px;
}

.instructions ol, .instructions ul {
  margin-bottom: 0;
  padding-left: 20px;
}

.instructions li {
  margin-bottom: 6px;
  line-height: 1.5;
}

.instructions ul li {
  list-style: none;
  position: relative;
  padding-left: 0;
}
</style>
